# MCP Server Template

A template for creating MCP (Model Context Protocol) servers using FastMCP. This provides a minimal server implementation with example tools that can be extended for specific use cases.

## Getting Started

### Prerequisites
- Python 3.10+
- [uv](https://docs.astral.sh/uv/) package manager

### Installation

1. Clone this repository
2. Install dependencies:
   ```bash
   uv add fastmcp
   ```
3. Configure environment variables (optional):
   ```bash
   cp .env.example .env
   # Edit .env with your specific values
   ```

### Local Development

#### Running the Server

Option 1 - Using the run script:
```bash
chmod +x run.sh
./run.sh
```

Option 2 - Direct execution:
```bash
uv run python main.py
```

Option 3 - Running the source directly:
```bash
uv run python src/server.py
```

## Testing with Claude

To test your MCP server with Claude:

1. Start the server locally using one of the run methods above
2. In Claude <PERSON>, add your MCP server to the configuration:
   ```json
   {
     "mcpServers": {
       "your-server-name": {
         "command": "uv",
         "args": ["run", "python", "/path/to/your/project/main.py"]
       }
     }
   }
   ```
3. Restart Claude <PERSON>ktop
4. Test the available tools:
   - `hello_world()` - Returns a simple greeting
   - `say_hello(name)` - Returns a personalized greeting

## Project Structure

```
├── src/
│   └── server.py          # Main MCP server implementation
├── main.py               # Entry point for Azure App Services
├── run.sh                # Local development runner script
├── pyproject.toml        # Python project configuration
└── README.md             # This file
```

## Deployment

### Azure App Services

This template is configured for Azure App Services deployment:

1. **Entry Point**: Uses `main.py` as the application entry point
2. **Dependencies**: Generate requirements.txt for Azure:
   ```bash
   uv export --format requirements-txt --output-file requirements.txt
   ```
3. **Deploy**: Upload to Azure App Services or deploy via GitHub Actions

> **Note**: Detailed Azure deployment instructions will be added based on specific deployment requirements.

## Customization

1. Update the server name in `src/server.py`
2. Add your custom tools using the `@mcp.tool` decorator
3. Modify the example tools or remove them entirely
4. Update this README with your specific project details