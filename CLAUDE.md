# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an MCP (Model Context Protocol) server template using FastMCP. The project provides a minimal server implementation with example tools for demonstrating MCP functionality.

## Architecture

- **Entry Point**: `src/server.py` - Main FastMCP server with tool definitions
- **Framework**: Uses FastMCP library for MCP server implementation
- **Tools**: Currently includes basic example tools (`hello_world` and `say_hello`)

## Development Setup

1. Install dependencies with `uv add` from the repository root (will create `pyproject.toml`)
2. The main dependency is `fastmcp` for the MCP server functionality
3. Run the server with `python src/server.py`

## Project Structure

```
├── src/
│   └── server.py          # Main MCP server implementation
├── README.md              # Template README (needs customization)
├── .gitignore            # Python gitignore
└── CLAUDE.md             # This file
```

## Key Implementation Details

- Server name is configurable via `server_name` variable in `src/server.py:8`
- Tools are defined using the `@mcp.tool` decorator
- Server runs via `mcp.run()` when executed directly
- Template includes TODO comments indicating areas that need customization

## Common Development Tasks

Since this is a template project, the README.md contains placeholder content that should be customized for specific implementations.